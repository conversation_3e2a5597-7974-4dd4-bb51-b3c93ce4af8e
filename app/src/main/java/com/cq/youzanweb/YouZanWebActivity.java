package com.cq.youzanweb;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.widget.Button;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.youzan.androidsdk.YouzanSDK;
import com.youzan.androidsdk.YouzanToken;
import com.youzan.androidsdk.YzLoginCallback;
import com.youzan.androidsdk.event.AbsAuthEvent;
import com.youzan.androidsdkx5.YouzanBrowser;

public class YouZanWebActivity extends AppCompatActivity {

    final String url = "https://shop153971273.youzan.com/v2/showcase/homepage?alias=vC1uZ46aH1&dc_ps=3900048604399832065.300001";
    private static final String TAG = "MainActivity";

    private final String noDataId = "1618831500345536";
    private final String haveDataId = "1616655761617822";

    private final String openUserId = noDataId;
    private final String avatar = "";
    private final String extra = "";
    private final String nickName = "";
    private final String gender = "";

    private YouzanBrowser youzanBrowser;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_you_zan_web);
        initView();
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
    }


    private void initView() {
        Button btnLoginYouZan = findViewById(R.id.btnLoginYouZan);
        btnLoginYouZan.setOnClickListener(v -> loginToTZ());
        Button btnLogoutYouZan = findViewById(R.id.btnLogoutYouZan);
        btnLogoutYouZan.setOnClickListener(v -> logoutToTZ());
        youzanBrowser = findViewById(R.id.youzan_web_view);
        youzanBrowser.subscribe(new AbsAuthEvent() {
            @Override
            public void call(Context context, boolean needLogin) {
                Log.e(TAG, "call: " + needLogin);
            }
        });
    }


    /**
     * 登录到有赞
     */
    private void loginToTZ() {
        YouzanSDK.yzlogin(openUserId, avatar, extra, nickName, gender, new YzLoginCallback() {
            @Override
            public void onSuccess(YouzanToken youzanToken) {
                Log.e(TAG, "onSuccess: " + youzanToken.toString());
                youzanBrowser.sync(youzanToken);
            }

            @Override
            public void onFail(String s) {
                Log.e(TAG, "onFail: " + s);
            }
        });
    }

    /**
     * 退出有赞
     */
    private void logoutToTZ() {
        YouzanSDK.userLogout(this);
    }
}