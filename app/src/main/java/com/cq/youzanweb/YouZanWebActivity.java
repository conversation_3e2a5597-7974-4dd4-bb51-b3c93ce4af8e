package com.cq.youzanweb;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.youzan.androidsdk.YouzanSDK;
import com.youzan.androidsdk.YouzanToken;
import com.youzan.androidsdk.YzLoginCallback;
import com.youzan.androidsdk.event.AbsAuthEvent;
import com.youzan.androidsdk.event.AbsAuthorizationErrorEvent;
import com.youzan.androidsdk.event.AbsAuthorizationSuccessEvent;
import com.youzan.androidsdkx5.YouzanBrowser;

public class YouZanWebActivity extends AppCompatActivity {

    final String url = "https://shop153971273.youzan.com/v2/showcase/homepage?alias=vC1uZ46aH1&dc_ps=3900048604399832065.300001";
    private static final String TAG = "YouZanWebActivity";

    private final String noDataId = "1618831500345536";
    private final String haveDataId = "1616655761617822";

    private final String openUserId = noDataId;
    private final String avatar = "";
    private final String extra = "";
    private final String nickName = "";
    private final String gender = "";

    private YouzanBrowser youzanBrowser;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_you_zan_web);
        initView();
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
    }


    private void initView() {
        Button btnLoginYouZan = findViewById(R.id.btnLoginYouZan);
        Button btnLogoutYouZan = findViewById(R.id.btnLogoutYouZan);
        Button btnLoadUrl = findViewById(R.id.btnLoadUrl);
        youzanBrowser = findViewById(R.id.youzan_web_view);
        btnLoginYouZan.setOnClickListener(v -> loginToTZ());
        btnLogoutYouZan.setOnClickListener(v -> logoutToTZ());
        btnLoadUrl.setOnClickListener(v -> {
            try {
                v.post(() -> youzanBrowser.loadUrl(url));
            } catch (Exception exception) {
                Log.e(TAG, "onClick: " + exception.toString());
            }
        });
        youzanBrowser.subscribe(new AbsAuthEvent() {
            @Override
            public void call(Context context, boolean needLogin) {
                Log.e(TAG, "AbsAuthEvent call: " + needLogin);
            }
        });
        youzanBrowser.subscribe(new AbsAuthorizationSuccessEvent() {
            @Override
            public void call(Context context) {
                Log.e(TAG, "AbsAuthorizationSuccessEvent call: ");
            }
        });
        youzanBrowser.subscribe(new AbsAuthorizationErrorEvent() {
            @Override
            public void call(Context context, int i, String s) {
                Log.e(TAG, "AbsAuthorizationErrorEvent call: ");
            }
        });
    }


    /**
     * 登录到有赞
     */
    private void loginToTZ() {
        YouzanSDK.yzlogin(openUserId, avatar, extra, nickName, gender, new YzLoginCallback() {
            @Override
            public void onSuccess(YouzanToken youzanToken) {
                Log.e(TAG, "login onSuccess: " + youzanToken.toString());
                // 确保WebView操作在主线程中执行
                youzanBrowser.post(() -> youzanBrowser.sync(youzanToken));
            }

            @Override
            public void onFail(String s) {
                Log.e(TAG, "login onFail: " + s);
            }
        });
    }

    /**
     * 退出有赞
     */
    private void logoutToTZ() {
        YouzanSDK.userLogout(this);
    }
}