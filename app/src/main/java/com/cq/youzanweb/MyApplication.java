package com.cq.youzanweb;

import android.app.Application;
import android.util.Log;

import com.youzan.androidsdk.InitCallBack;
import com.youzan.androidsdk.InitConfig;
import com.youzan.androidsdk.YouzanSDK;
import com.youzan.androidsdkx5.YouZanSDKX5Adapter;

public class MyApplication extends Application {
    private static final String TAG = "MyApplication";


    private final String releaseKey = "ef147e6a829f41a9885101a670a7b73b";

    @Override
    public void onCreate() {
        super.onCreate();
        String debugKey = "4fba2686d9ec4696ad42aa5deebc2c67";
        InitConfig initConfig = InitConfig.builder()
                .clientId("3de7355c8465687071")
                .appkey(debugKey)
                .adapter(new YouZanSDKX5Adapter())
                .initCallBack((b, s) -> {
                    Log.e(TAG, "onCreate: 初始化回调：" + b + " " + s);
                }).advanceHideX5Loading(false)
                .build();
        YouzanSDK.init(this, initConfig);
    }
}
